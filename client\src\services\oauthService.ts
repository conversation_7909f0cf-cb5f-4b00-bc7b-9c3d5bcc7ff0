import axios from 'axios'
import type {
  AuthError,
  LoginCredentials,
  TokenResponse,
  User,
} from '../types/oauth'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3230'

// OAuth client configuration for admin interface
const OAUTH_CLIENT_ID = import.meta.env.VITE_OAUTH_CLIENT_ID || 'varpro-admin'
const OAUTH_CLIENT_SECRET =
  import.meta.env.VITE_OAUTH_CLIENT_SECRET || 'varpro-admin-secret'

class OAuthService {
  private token: string | null = null

  constructor() {
    // Load token from localStorage on initialization
    this.token = localStorage.getItem('access_token')
  }

  // Resource Owner Password Credentials Flow
  async login(credentials: LoginCredentials): Promise<TokenResponse> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/oauth/token`,
        {
          grant_type: 'password',
          username: credentials.username,
          password: credentials.password,
          client_id: OAUTH_CLIENT_ID,
          client_secret: OAUTH_CLIENT_SECRET,
          scope: 'read write',
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          transformRequest: [
            (data) => {
              return Object.keys(data)
                .map(
                  (key) =>
                    `${encodeURIComponent(key)}=${encodeURIComponent(
                      data[key]
                    )}`
                )
                .join('&')
            },
          ],
        }
      )

      const tokenData: TokenResponse = response.data

      // Store tokens
      this.token = tokenData.access_token
      localStorage.setItem('access_token', tokenData.access_token)
      if (tokenData.refresh_token) {
        localStorage.setItem('refresh_token', tokenData.refresh_token)
      }

      return tokenData
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        const authError: AuthError = error.response.data
        throw new Error(
          authError.error_description || authError.error || 'Login failed'
        )
      }
      throw new Error('Network error occurred')
    }
  }

  // Refresh access token
  async refreshToken(): Promise<TokenResponse> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await axios.post(
        `${API_BASE_URL}/oauth/token`,
        {
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
          client_id: OAUTH_CLIENT_ID,
          client_secret: OAUTH_CLIENT_SECRET,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          transformRequest: [
            (data) => {
              return Object.keys(data)
                .map(
                  (key) =>
                    `${encodeURIComponent(key)}=${encodeURIComponent(
                      data[key]
                    )}`
                )
                .join('&')
            },
          ],
        }
      )

      const tokenData: TokenResponse = response.data

      // Update stored tokens
      this.token = tokenData.access_token
      localStorage.setItem('access_token', tokenData.access_token)

      return tokenData
    } catch (error) {
      // If refresh fails, clear all tokens
      this.logout()
      throw new Error('Session expired. Please login again.')
    }
  }

  // Logout and revoke tokens
  async logout(): Promise<void> {
    const accessToken = this.token || localStorage.getItem('access_token')

    if (accessToken) {
      try {
        await axios.post(
          `${API_BASE_URL}/oauth/revoke`,
          {
            token: accessToken,
            client_id: OAUTH_CLIENT_ID,
            client_secret: OAUTH_CLIENT_SECRET,
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            transformRequest: [
              (data) => {
                return Object.keys(data)
                  .map(
                    (key) =>
                      `${encodeURIComponent(key)}=${encodeURIComponent(
                        data[key]
                      )}`
                  )
                  .join('&')
              },
            ],
          }
        )
      } catch (error) {
        console.warn('Failed to revoke token:', error)
      }
    }

    // Clear local storage and state
    this.token = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }

  // Get current user info (this would need to be implemented on the server)
  async getCurrentUser(): Promise<User> {
    if (!this.token) {
      throw new Error('No access token available')
    }

    try {
      const response = await axios.get(`${API_BASE_URL}/api/v1/users/me`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
        },
      })

      return response.data
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        // Try to refresh token
        await this.refreshToken()
        // Retry the request
        const response = await axios.get(`${API_BASE_URL}/api/v1/users/me`, {
          headers: {
            Authorization: `Bearer ${this.token}`,
          },
        })
        return response.data
      }
      throw error
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.token
  }

  // Get current access token
  getAccessToken(): string | null {
    return this.token
  }

  // Authorization Code Flow (for future use)
  getAuthorizationUrl(redirectUri: string, state?: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: OAUTH_CLIENT_ID,
      redirect_uri: redirectUri,
      scope: 'read write',
      ...(state && { state }),
    })

    return `${API_BASE_URL}/oauth/authorize?${params.toString()}`
  }

  // Exchange authorization code for tokens (for future use)
  async exchangeCodeForTokens(
    code: string,
    redirectUri: string
  ): Promise<TokenResponse> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/oauth/token`,
        {
          grant_type: 'authorization_code',
          code,
          redirect_uri: redirectUri,
          client_id: OAUTH_CLIENT_ID,
          client_secret: OAUTH_CLIENT_SECRET,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          transformRequest: [
            (data) => {
              return Object.keys(data)
                .map(
                  (key) =>
                    `${encodeURIComponent(key)}=${encodeURIComponent(
                      data[key]
                    )}`
                )
                .join('&')
            },
          ],
        }
      )

      const tokenData: TokenResponse = response.data

      // Store tokens
      this.token = tokenData.access_token
      localStorage.setItem('access_token', tokenData.access_token)
      if (tokenData.refresh_token) {
        localStorage.setItem('refresh_token', tokenData.refresh_token)
      }

      return tokenData
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        const authError: AuthError = error.response.data
        throw new Error(
          authError.error_description ||
            authError.error ||
            'Token exchange failed'
        )
      }
      throw new Error('Network error occurred')
    }
  }
}

export const oauthService = new OAuthService()
