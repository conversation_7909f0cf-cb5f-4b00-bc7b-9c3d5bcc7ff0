import { Router } from 'express'
import * as clientController from '../../controllers/clientController'

const router = Router()

// GET /api/v1/clients - Get all OAuth clients
router.get('/', clientController.getAllClients)

// GET /api/v1/clients/:id - Get client by ID
router.get('/:id', clientController.getClientById)

// POST /api/v1/clients - Create new OAuth client
router.post('/', clientController.createClient)

// PUT /api/v1/clients/:id - Update OAuth client
router.put('/:id', clientController.updateClient)

// DELETE /api/v1/clients/:id - Delete OAuth client (soft delete)
router.delete('/:id', clientController.deleteClient)

export default router
