import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import { oauthClientService } from '../services/oauthClientService'

export const getAllClients = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const clients = await oauthClientService.getAllClients()
    res.json(clients)
  } catch (error) {
    console.error('Error getting clients:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve clients',
    })
  }
}

export const getClientById = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
    }

    const client = await oauthClientService.getClientById(id)
    if (!client) {
      return res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
    }

    res.json(client)
  } catch (error) {
    console.error('Error getting client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to retrieve client',
    })
  }
}

export const createClient = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events
    } = req.body

    if (!client_name || !redirect_uris || !grant_types || !scope) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Client name, redirect URIs, grant types, and scope are required',
      })
    }

    // Validate grant types
    const validGrantTypes = ['authorization_code', 'password', 'refresh_token', 'client_credentials']
    const invalidGrantTypes = grant_types.filter((type: string) => !validGrantTypes.includes(type))
    if (invalidGrantTypes.length > 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: `Invalid grant types: ${invalidGrantTypes.join(', ')}`,
      })
    }

    // Validate redirect URIs (basic URL validation)
    if (!Array.isArray(redirect_uris) || redirect_uris.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'At least one redirect URI is required',
      })
    }

    for (const uri of redirect_uris) {
      try {
        new URL(uri)
      } catch {
        return res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: `Invalid redirect URI: ${uri}`,
        })
      }
    }

    // Validate webhook events if provided
    if (webhook_events && !Array.isArray(webhook_events)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'webhook_events must be an array',
      })
    }

    // Validate webhook URL if provided
    if (webhook_url) {
      try {
        new URL(webhook_url)
      } catch {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'Invalid webhook URL',
        })
        return
      }
    }

    const client = await oauthClientService.createClient({
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    })

    res.status(StatusCodes.CREATED).json(client)
  } catch (error) {
    console.error('Error creating client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to create client',
    })
  }
}

export const updateClient = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
      return
    }

    const {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events
    } = req.body

    // Check if client exists
    const existingClient = await oauthClientService.getClientById(id)
    if (!existingClient) {
      return res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
    }

    // Validate grant types if provided
    if (grant_types) {
      const validGrantTypes = ['authorization_code', 'password', 'refresh_token', 'client_credentials']
      const invalidGrantTypes = grant_types.filter((type: string) => !validGrantTypes.includes(type))
      if (invalidGrantTypes.length > 0) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: `Invalid grant types: ${invalidGrantTypes.join(', ')}`,
        })
      }
    }

    // Validate redirect URIs if provided
    if (redirect_uris) {
      if (!Array.isArray(redirect_uris) || redirect_uris.length === 0) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'At least one redirect URI is required',
        })
      }

      for (const uri of redirect_uris) {
        try {
          new URL(uri)
        } catch {
          return res.status(StatusCodes.BAD_REQUEST).json({
            error: 'invalid_request',
            message: `Invalid redirect URI: ${uri}`,
          })
        }
      }
    }

    // Validate webhook events if provided
    if (webhook_events && !Array.isArray(webhook_events)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'webhook_events must be an array',
      })
    }

    // Validate webhook URL if provided
    if (webhook_url) {
      try {
        new URL(webhook_url)
      } catch {
        return res.status(StatusCodes.BAD_REQUEST).json({
          error: 'invalid_request',
          message: 'Invalid webhook URL',
        })
      }
    }

    const updatedClient = await oauthClientService.updateClient(id, {
      client_name,
      redirect_uris,
      grant_types,
      scope,
      is_active,
      webhook_url,
      webhook_secret,
      webhook_events,
    })

    if (!updatedClient) {
      return res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
    }

    res.json(updatedClient)
  } catch (error) {
    console.error('Error updating client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to update client',
    })
  }
}

export const deleteClient = async (req: Request, res: Response): Promise<Response | void> => {
  try {
    const id = parseInt(req.params.id)
    if (isNaN(id)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        message: 'Invalid client ID',
      })
    }

    const success = await oauthClientService.deleteClient(id)
    if (!success) {
      return res.status(StatusCodes.NOT_FOUND).json({
        error: 'not_found',
        message: 'Client not found',
      })
    }

    res.status(StatusCodes.NO_CONTENT).send()
  } catch (error) {
    console.error('Error deleting client:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      message: 'Failed to delete client',
    })
  }
}
